<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Field definitions for the migration
     */
    private function getFieldDefinitions(): array
    {
        return [
            'public_id' => [
                'type' => 'string',
                'length' => 26,
                'nullable' => true,
                'unique' => true,
                'after' => 'id',
                'index' => true,
            ],
            'slug' => [
                'type' => 'string',
                'length' => 100,
                'nullable' => true,
                'unique' => true,
                'after' => 'name',
                'index' => true,
            ],
            'deleted_at' => [
                'type' => 'softDeletes',
                'nullable' => true,
            ],
            'created_by' => [
                'type' => 'unsignedBigInteger',
                'nullable' => true,
                'after' => 'created_at',
                'index' => true,
                'foreign' => ['references' => 'id', 'on' => 'users', 'onDelete' => 'set null'],
            ],
            'updated_by' => [
                'type' => 'unsignedBigInteger',
                'nullable' => true,
                'after' => 'updated_at',
                'index' => true,
                'foreign' => ['references' => 'id', 'on' => 'users', 'onDelete' => 'set null'],
            ],
            'deleted_by' => [
                'type' => 'unsignedBigInteger',
                'nullable' => true,
                'after' => 'deleted_at',
                'index' => true,
                'foreign' => ['references' => 'id', 'on' => 'users', 'onDelete' => 'set null'],
            ],
            'app_authentication_secret' => [
                'type' => 'text',
                'nullable' => true,
                'after' => 'remember_token',
            ],
            'app_authentication_recovery_codes' => [
                'type' => 'text',
                'nullable' => true,
                'after' => 'app_authentication_secret',
            ],
        ];
    }

    /**
     * Add a column based on field definition
     */
    private function addColumn(Blueprint $table, string $fieldName, array $definition): void
    {
        $column = match ($definition['type']) {
            'string' => $table->string($fieldName, $definition['length'] ?? 255),
            'text' => $table->text($fieldName),
            'unsignedBigInteger' => $table->unsignedBigInteger($fieldName),
            'softDeletes' => $table->softDeletes(),
            default => throw new \InvalidArgumentException("Unsupported column type: {$definition['type']}")
        };

        // Apply modifiers only if column was created (not for softDeletes)
        if ($definition['type'] !== 'softDeletes') {
            if ($definition['nullable'] ?? false) {
                $column->nullable();
            }
            if ($definition['unique'] ?? false) {
                $column->unique();
            }
            if (isset($definition['after'])) {
                $column->after($definition['after']);
            }
        }
    }

    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $fields = $this->getFieldDefinitions();

        // Add columns
        Schema::table('users', function (Blueprint $table) use ($fields) {
            foreach ($fields as $fieldName => $definition) {
                if (!Schema::hasColumn('users', $fieldName)) {
                    $this->addColumn($table, $fieldName, $definition);
                }
            }
        });

        // Add indexes and foreign keys with Laravel's built-in resilience
        Schema::table('users', function (Blueprint $table) use ($fields) {
            foreach ($fields as $fieldName => $definition) {
                if (!Schema::hasColumn('users', $fieldName)) {
                    continue;
                }

                // Add index if specified
                if ($definition['index'] ?? false) {
                    try {
                        $table->index($fieldName);
                    } catch (\Throwable $e) {
                        // Index likely already exists, continue
                    }
                }

                // Add foreign key if specified
                if (isset($definition['foreign'])) {
                    $foreign = $definition['foreign'];
                    try {
                        $table->foreign($fieldName)
                            ->references($foreign['references'])
                            ->on($foreign['on'])
                            ->onDelete($foreign['onDelete']);
                    } catch (\Throwable $e) {
                        // Foreign key likely already exists, continue
                    }
                }
            }
        });
    }


    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $fields = $this->getFieldDefinitions();

        // Drop foreign keys first
        Schema::table('users', function (Blueprint $table) use ($fields) {
            foreach ($fields as $fieldName => $definition) {
                if (!Schema::hasColumn('users', $fieldName)) {
                    continue;
                }

                if (isset($definition['foreign'])) {
                    try {
                        $table->dropForeign([$fieldName]);
                    } catch (\Throwable $e) {
                        // Foreign key likely doesn't exist, continue
                    }
                }
            }
        });

        // Drop indexes
        Schema::table('users', function (Blueprint $table) use ($fields) {
            foreach ($fields as $fieldName => $definition) {
                if (!Schema::hasColumn('users', $fieldName)) {
                    continue;
                }

                if ($definition['index'] ?? false) {
                    $this->dropIndexSafely($table, 'users', $fieldName, $definition['unique'] ?? false);
                }
            }
        });

        // Drop columns in bulk with fallback to individual drops
        $this->dropColumnsWithFallback($fields);
    }

    /**
     * Drop index safely with multiple naming conventions
     */
    private function dropIndexSafely(Blueprint $table, string $tableName, string $column, bool $isUnique = false): void
    {
        $indexNames = [
            $column,
            "{$tableName}_{$column}_index",
        ];

        if ($isUnique) {
            $indexNames[] = "{$tableName}_{$column}_unique";
        }

        foreach ($indexNames as $indexName) {
            try {
                $table->dropIndex([$indexName]);
                return; // Success, exit early
            } catch (\Throwable $e) {
                continue; // Try next naming convention
            }
        }
    }

    /**
     * Drop columns with bulk operation and individual fallback
     */
    private function dropColumnsWithFallback(array $fields): void
    {
        $columnsToDrop = array_filter(
            array_keys($fields),
            fn($col) => Schema::hasColumn('users', $col)
        );

        if (empty($columnsToDrop)) {
            return;
        }

        // Try bulk drop first
        try {
            Schema::table('users', function (Blueprint $table) use ($columnsToDrop) {
                $table->dropColumn($columnsToDrop);
            });
        } catch (\Throwable $e) {
            // Fallback to individual column drops
            foreach ($columnsToDrop as $column) {
                if (Schema::hasColumn('users', $column)) {
                    try {
                        Schema::table('users', function (Blueprint $table) use ($column) {
                            $table->dropColumn($column);
                        });
                    } catch (\Throwable $e) {
                        // Log or handle individual column drop failure if needed
                        // For now, silently continue to maintain resilience
                    }
                }
            }
        }
    }
};
